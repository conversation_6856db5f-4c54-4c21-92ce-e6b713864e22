# 智能浮标模拟器说明文档

## 1. 概述

智能浮标模拟器是一个用于模拟真实浮标设备行为的软件工具，可以生成模拟的传感器数据、位置信息，并响应控制命令。该模拟器主要用于开发和测试智能浮标管理与互动平台，无需实际部署物理浮标设备即可进行系统功能验证。

## 2. 功能特性

### 2.1 基本功能

- **多浮标模拟**：支持同时模拟多个浮标设备，每个浮标独立运行
- **传感器数据生成**：模拟生成多种环境参数数据，包括：
  - 温度 (°C)
  - pH值
  - 溶解氧 (mg/L)
  - 水位 (m)
  - 流速 (m/s)
- **位置数据生成**：模拟浮标在指定基准位置附近的随机漂移
- **控制命令响应**：接收并处理外部控制命令，如设置LED灯亮度和颜色
- **心跳机制**：定期发送心跳消息，表明浮标在线状态
- **自定义传感器**：以上未涉及的真实传感器数据可按照类似格式上报（请将实现的格式提供给瀚界，以便在服务器端进行支持）

### 2.2 通信协议

- **MQTT通信**：使用MQTT协议进行数据传输和命令接收
- **JSON数据格式**：所有数据和命令均使用JSON格式编码
- **支持认证**：支持MQTT用户名/密码认证
- **TLS加密**：后期服务器会开启 TLS 验证，以确保安全

### 2.3 高级特性

- **可配置参数**：通过环境变量或配置文件灵活配置模拟器行为
- **随机数据生成**：生成的数据具有一定随机性，模拟真实环境变化
- **优雅关闭**：支持信号处理和优雅关闭机制，确保资源正确释放
- **错误处理**：完善的异常捕获和日志记录机制

## 3. 技术实现

### 3.1 架构设计

模拟器采用多线程架构，主要组件包括：

- **BuoySimulator类**：单个浮标模拟器的核心类，负责数据生成和通信
- **MQTT客户端**：负责与MQTT服务器通信，发送数据和接收命令
- **主线程**：负责创建和管理多个浮标模拟器实例
- **信号处理器**：处理系统信号，确保程序能够优雅退出

### 3.2 数据流

1. **传感器数据流**：
   - 浮标模拟器生成随机传感器数据
   - 数据格式化为JSON
   - 通过MQTT发布到对应主题：`buoy/data/{buoy_id}/{data_type}`

2. **位置数据流**：
   - 浮标模拟器生成随机位置数据（基于基准位置的偏移）
   - 数据格式化为JSON
   - 通过MQTT发布到位置主题：`buoy/data/{buoy_id}/location`

3. **控制命令流**：
   - 浮标模拟器订阅控制主题：`buoy/command/{buoy_id}`
   - 接收到命令后解析JSON内容
   - 执行相应操作（如设置灯光参数）
   - 发送执行结果到响应主题：`buoy/command/response/{buoy_id}`

4. **心跳机制**：
   - 浮标模拟器定期发送心跳消息
   - 心跳消息包含浮标ID、时间戳和状态信息
   - 通过MQTT发布到心跳主题：`buoy/heartbeat/{buoy_id}`

## 4. 配置说明

### 4.1 环境变量

模拟器可通过以下环境变量进行配置：

| 环境变量 | 说明 | 默认值 |
|---------|------|-------|
| MQTT_BROKER_HOST | MQTT服务器地址 | mqtt |
| MQTT_BROKER_PORT | MQTT服务器端口 | 1883 |
| MQTT_USERNAME | MQTT用户名 | (空) |
| MQTT_PASSWORD | MQTT密码 | (空) |
| SIM_BUOY_COUNT | 模拟的浮标数量 | 1 |
| SIM_DATA_INTERVAL_MIN | 数据发送最小间隔(秒) | 2 |
| SIM_DATA_INTERVAL_MAX | 数据发送最大间隔(秒) | 5 |
| SIM_TOPIC_PREFIX | 主题前缀 | buoy/data/ |
| SIM_BASE_LATITUDE | 基准纬度 | 31.2304 |
| SIM_BASE_LONGITUDE | 基准经度 | 121.4737 |
| LOG_LEVEL | 日志级别 | INFO |

### 4.2 配置文件

除了环境变量外，也可以通过`.env`文件进行配置：

```
MQTT_BROKER_HOST=mqtt
MQTT_BROKER_PORT=1883
MQTT_USERNAME=user
MQTT_PASSWORD=password
SIM_BUOY_COUNT=3
SIM_DATA_INTERVAL_MIN=5
SIM_DATA_INTERVAL_MAX=10
```

## 5. 消息格式

### 5.1 传感器数据消息

**主题**：`buoy/data/{buoy_id}/{data_type}`

**示例**：`buoy/data/1/temperature`

**内容**：
```json
{
  "buoy_id": 1,
  "timestamp": "2025-04-01T08:30:45.123Z",
  "data_type": "temperature",
  "value": 25.5,
  "unit": "°C"
}
```

### 5.2 位置数据消息

**主题**：`buoy/data/{buoy_id}/location`

**示例**：`buoy/data/1/location`

**内容**：
```json
{
  "buoy_id": 1,
  "timestamp": "2025-04-01T08:30:45.123Z",
  "data_type": "location",
  "value": {
    "longitude": 121.4737,
    "latitude": 31.2304
  },
  "unit": "WGS84"
}
```

### 5.3 心跳消息

**主题**：`buoy/heartbeat/{buoy_id}`

**示例**：`buoy/heartbeat/1`

**内容**：
```json
{
  "buoy_id": 1,
  "timestamp": "2025-04-01T08:30:45.123Z",
  "status": "active"
}
```

### 5.4 控制命令

**主题**：`buoy/command/{buoy_id}`

**示例**：`buoy/command/1`

**内容**：
```json
{
  "command": "set_light",
  "brightness": 75,
  "color": "#FF8C00"
}
```

### 5.5 控制响应

**主题**：`buoy/command/response/{buoy_id}`

**示例**：`buoy/command/response/1`

**内容**：
```json
{
  "buoy_id": 1,
  "timestamp": "2025-04-01T08:30:45.123Z",
  "status": "success",
  "command": "set_light",
  "current_settings": {
    "brightness": 75,
    "color": "#FF8C00"
  }
}
```

## 6. 使用指南

### 6.1 启动模拟器

#### 单独启动模拟器

```bash
cd simulator
docker build -t buoy-simulator .
docker run -d --name buoy-simulator --network=host \
  -e SIM_BUOY_COUNT=3 \
  -e MQTT_BROKER_HOST=localhost \
  buoy-simulator
```

### 6.2 发送控制命令

可以使用MQTT客户端工具（如MQTT Explorer、mosquitto_pub等）向浮标发送控制命令：

```bash
mosquitto_pub -h localhost -t "buoy/command/1" -m '{"command":"set_light","brightness":75,"color":"#FF8C00"}'
```

### 6.3 查看模拟器日志

```bash
docker logs -f buoy-simulator
```

## 7. 开发与扩展

### 7.1 添加新的传感器类型

在`generate_sensor_data`方法中添加新的传感器类型：

```python
def generate_sensor_data(self):
    """生成模拟的传感器数据"""
    data_types = {
        "temperature": {"value": round(random.uniform(15, 30), 2), "unit": "°C"},
        "ph": {"value": round(random.uniform(6.5, 8.5), 2), "unit": "pH"},
        "dissolved_oxygen": {"value": round(random.uniform(5, 12), 2), "unit": "mg/L"},
        "water_level": {"value": round(random.uniform(0.5, 2.5), 2), "unit": "m"},
        "flow_rate": {"value": round(random.uniform(0.1, 3.0), 2), "unit": "m/s"},
        # 添加新的传感器类型
        "turbidity": {"value": round(random.uniform(0, 100), 2), "unit": "NTU"}
    }

    # 随机选择一个或多个数据类型
    return {k: v for k, v in data_types.items() if random.random() > 0.3}
```

### 7.2 添加新的控制命令

在`handle_control_command`方法中添加新的命令处理逻辑：

```python
def handle_control_command(self, command):
    """处理接收到的控制命令"""
    if "command" not in command:
        return

    if command["command"] == "set_light":
        # 处理设置灯光命令
        # ...
    elif command["command"] == "set_data_frequency":
        # 处理设置数据频率命令
        if "frequency" in command:
            # 实现频率调整逻辑
            pass
```

## 8. 已知限制与未来改进

### 8.1 当前限制

- 未实现图像数据采集与上传功能
- 未实现动态调整数据采集频率的功能
- 未配置TLS加密连接
- 重连机制不完善

### 8.2 计划改进

- 添加图像模拟与HTTP上传功能
- 增加TLS加密支持
- 实现动态调整数据采集频率的功能
- 完善断线重连机制
- 添加更多控制命令支持（如重启、立即拍照等）

## 9. 故障排除

### 9.1 常见问题

1. **无法连接到MQTT服务器**
   - 检查MQTT服务器地址和端口是否正确
   - 确认MQTT服务器是否运行
   - 检查网络连接和防火墙设置

2. **未收到浮标数据**
   - 确认浮标模拟器已成功启动
   - 检查MQTT订阅主题是否正确
   - 查看浮标模拟器日志是否有错误信息

3. **控制命令无响应**
   - 确认控制命令格式是否正确
   - 检查控制主题是否正确
   - 查看浮标模拟器日志是否接收到命令

### 9.2 日志级别调整

可以通过设置`LOG_LEVEL`环境变量调整日志级别，以获取更详细的调试信息：

```bash
docker run -d --name buoy-simulator -e LOG_LEVEL=DEBUG buoy-simulator
```

## 10. 参考资料

- [MQTT协议规范](http://mqtt.org/)
- [JSON数据格式](https://www.json.org/)
- [Paho MQTT客户端库](https://www.eclipse.org/paho/)
- [Docker容器化部署](https://docs.docker.com/)
